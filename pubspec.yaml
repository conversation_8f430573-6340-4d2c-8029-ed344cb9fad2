name: platix
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.8+1

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  intl:
  country_state_city: ^0.1.6
  file_picker: ^10.2.0
  url_launcher: ^6.3.1
  shared_preferences: ^2.3.5
  dropdown_button2: ^2.3.9
  cached_network_image: ^3.4.1
  flutter_svg: ^2.0.17
  pin_code_fields: ^8.0.1
  flutter_typeahead: ^5.2.0
  smooth_page_indicator: ^1.2.0+3
  dropdown_search: ^5.0.6
  http: ^1.4.0
  mime: ^2.0.0
  http_parser: ^4.0.2
  flutter_spinkit: ^5.2.1
  get: ^4.6.6
  provider: ^6.1.2
  geolocator: ^14.0.1
  geocoding: ^4.0.0
  google_places_flutter: ^2.0.5
  google_maps_flutter: ^2.2.0
  image_picker: ^1.1.2
  equatable: ^2.0.7
  debounce_throttle: ^2.0.0
  pointer_interceptor: ^0.10.1+2
  get_storage: ^2.1.1
  file_saver: ^0.2.14
  excel: ^4.0.6
  pdf: ^3.11.3
  simple_file_saver: ^1.0.0
  path_provider: ^2.1.5
  open_file: ^3.5.10
  firebase_core: ^3.13.1
  firebase_auth: ^5.5.4
  onesignal_flutter: ^5.0.4
  fluttertoast: ^8.1.1
  intl_phone_field: ^3.0.0
  flutter_cashfree_pg_sdk: ^2.2.4+42
  permission_handler: ^12.0.0+1
  image: ^4.1.3
  flutter_html: ^3.0.0-beta.2
  flutter_dotenv: ^5.2.1
  photo_view: ^0.15.0
  share_plus: ^11.0.0
  sms_autofill: ^2.3.0
  webview_flutter: ^4.13.0
  collection: ^1.19.1
  badges: ^3.1.2
  fl_chart: ^0.68.0
  table_calendar: ^3.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/
    - assets/illustrations/
    - assets/images/
    - assets/gif/
    - .env

  #  fonts:
  #    - family: Poppins
  #      fonts:
  #        - asset: fonts/PoppinsRegular.ttf
  #          weight: 400
  #        - asset: fonts/PoppinsMedium.ttf
  #          weight: 500

  fonts:
    - family: Montserrat
      fonts:
        - asset: fonts/Montserrat/Montserrat-Regular.ttf
        - asset: fonts/Montserrat/Montserrat-Italic.ttf
          style: italic
        - asset: fonts/Montserrat/Montserrat-Medium.ttf
          weight: 500
        - asset: fonts/Montserrat/Montserrat-SemiBold.ttf
          weight: 600
        - asset: fonts/Montserrat/Montserrat-Bold.ttf
          weight: 700

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
