import 'package:flutter/material.dart';

// Simple country data structure
class CountryData {
  final String name;
  final String code;
  final String dialCode;
  final String flag;

  CountryData({
    required this.name,
    required this.code,
    required this.dialCode,
    required this.flag,
  });
}

class CustomPhoneNumber extends StatefulWidget {
  final String? initialCountryCode;
  final Function(CountryData) onTap;

  const CustomPhoneNumber({
    super.key,
    this.initialCountryCode,
    required this.onTap,
  });

  @override
  State<CustomPhoneNumber> createState() => _CustomPhoneNumberState();
}

class _CustomPhoneNumberState extends State<CustomPhoneNumber> {
  CountryData selectedCountry = CountryData(
    name: 'India',
    code: 'IN',
    dialCode: '+91',
    flag: '🇮🇳',
  );

  // Common countries list
  final List<CountryData> countries = [
    CountryData(name: 'India', code: 'IN', dialCode: '+91', flag: '🇮🇳'),
    CountryData(name: 'United States', code: 'US', dialCode: '+1', flag: '🇺🇸'),
    CountryData(name: 'United Kingdom', code: 'GB', dialCode: '+44', flag: '🇬🇧'),
    CountryData(name: 'Canada', code: 'CA', dialCode: '+1', flag: '🇨🇦'),
    CountryData(name: 'Australia', code: 'AU', dialCode: '+61', flag: '🇦🇺'),
    CountryData(name: 'Germany', code: 'DE', dialCode: '+49', flag: '🇩🇪'),
    CountryData(name: 'France', code: 'FR', dialCode: '+33', flag: '🇫🇷'),
    CountryData(name: 'Japan', code: 'JP', dialCode: '+81', flag: '🇯🇵'),
    CountryData(name: 'China', code: 'CN', dialCode: '+86', flag: '🇨🇳'),
    CountryData(name: 'Brazil', code: 'BR', dialCode: '+55', flag: '🇧🇷'),
  ];

  @override
  void initState() {
    super.initState();
    if (widget.initialCountryCode != null) {
      final country = countries.firstWhere(
        (c) => c.dialCode == widget.initialCountryCode,
        orElse: () => selectedCountry,
      );
      selectedCountry = country;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _showCountryPicker,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              selectedCountry.flag,
              style: const TextStyle(fontSize: 20),
            ),
            const SizedBox(width: 8),
            Text(
              selectedCountry.dialCode,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(width: 4),
            const Icon(Icons.arrow_drop_down),
          ],
        ),
      ),
    );
  }

  void _showCountryPicker() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Country'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: ListView.builder(
              itemCount: countries.length,
              itemBuilder: (context, index) {
                final country = countries[index];
                return ListTile(
                  leading: Text(
                    country.flag,
                    style: const TextStyle(fontSize: 24),
                  ),
                  title: Text(country.name),
                  subtitle: Text(country.dialCode),
                  onTap: () {
                    setState(() {
                      selectedCountry = country;
                    });
                    widget.onTap(country);
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }
}
