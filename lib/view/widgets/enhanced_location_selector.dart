import 'package:flutter/material.dart';
import 'package:country_state_city/country_state_city.dart' as csc;
import 'package:platix/utils/app_export.dart';

class EnhancedLocationSelector extends StatefulWidget {
  final String? initialCountryId;
  final String? initialStateId;
  final String? initialCityName;
  final Function(String? countryId, String countryName) onCountryChanged;
  final Function(String? stateId, String stateName) onStateChanged;
  final Function(String cityName) onCityChanged;

  const EnhancedLocationSelector({
    super.key,
    this.initialCountryId,
    this.initialStateId,
    this.initialCityName,
    required this.onCountryChanged,
    required this.onStateChanged,
    required this.onCityChanged,
  });

  @override
  State<EnhancedLocationSelector> createState() => _EnhancedLocationSelectorState();
}

class _EnhancedLocationSelectorState extends State<EnhancedLocationSelector> {
  List<csc.Country> countries = [];
  List<csc.State> states = [];
  List<csc.City> cities = [];
  
  csc.Country? selectedCountry;
  csc.State? selectedState;
  String? selectedCityName;
  
  bool isLoadingCountries = true;
  bool isLoadingStates = false;
  bool isLoadingCities = false;

  @override
  void initState() {
    super.initState();
    _loadCountries();
  }

  Future<void> _loadCountries() async {
    setState(() => isLoadingCountries = true);
    try {
      countries = await csc.getAllCountries();
      
      // Set initial country if provided
      if (widget.initialCountryId != null) {
        selectedCountry = countries.firstWhere(
          (country) => country.isoCode == widget.initialCountryId,
          orElse: () => countries.first,
        );
        await _loadStates();
      }
    } catch (e) {
      debugPrint('Error loading countries: $e');
    } finally {
      setState(() => isLoadingCountries = false);
    }
  }

  Future<void> _loadStates() async {
    if (selectedCountry == null) return;
    
    setState(() => isLoadingStates = true);
    try {
      states = await csc.getStatesOfCountry(selectedCountry!.isoCode);
      
      // Set initial state if provided
      if (widget.initialStateId != null) {
        selectedState = states.firstWhere(
          (state) => state.isoCode == widget.initialStateId,
          orElse: () => states.isNotEmpty ? states.first : csc.State(
            name: '', 
            isoCode: '', 
            countryCode: selectedCountry?.isoCode ?? ''
          ),
        );
        if (selectedState != null && selectedState!.isoCode.isNotEmpty) {
          await _loadCities();
        }
      } else {
        selectedState = null;
        cities = [];
        selectedCityName = null;
      }
    } catch (e) {
      debugPrint('Error loading states: $e');
    } finally {
      setState(() => isLoadingStates = false);
    }
  }

  Future<void> _loadCities() async {
    if (selectedCountry == null || selectedState == null) return;

    setState(() => isLoadingCities = true);
    try {
      final stateCities = await csc.getStateCities(selectedState!.countryCode, selectedState!.isoCode);
      cities = stateCities;

      // Set initial city if provided
      if (widget.initialCityName != null) {
        final initialCity = cities.firstWhere(
          (city) => city.name == widget.initialCityName,
          orElse: () => cities.isNotEmpty ? cities.first : csc.City(name: '', countryCode: '', stateCode: ''),
        );
        if (initialCity.name.isNotEmpty) {
          selectedCityName = initialCity.name;
        } else {
          selectedCityName = null;
        }
      } else {
        selectedCityName = null;
      }

      debugPrint('Cities loaded: ${cities.length} cities for state: ${selectedState!.name}');
    } catch (e) {
      debugPrint('Error loading cities: $e');
      cities = [];
      selectedCityName = null;
    } finally {
      setState(() => isLoadingCities = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Country Dropdown
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Country', style: CustomTextStyles.b4_1),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: () => _showCountryDialog(),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      selectedCountry?.name ?? 'Select Country',
                      style: TextStyle(
                        color: selectedCountry != null ? Colors.black : Colors.grey,
                        fontSize: 14,
                      ),
                    ),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 15),
        
        // State Dropdown
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('State', style: CustomTextStyles.b4_1),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: selectedCountry != null ? () => _showStateDialog() : null,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                  color: selectedCountry != null ? Colors.white : Colors.grey.shade100,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    isLoadingStates
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(
                            selectedState?.name ?? 'Select State',
                            style: TextStyle(
                              color: selectedState != null ? Colors.black : Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 15),
        
        // City Dropdown
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('City', style: CustomTextStyles.b4_1),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: selectedState != null ? () => _showCityDialog() : null,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                  color: selectedState != null ? Colors.white : Colors.grey.shade100,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    isLoadingCities
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(
                            selectedCityName ?? 'Enter City Name',
                            style: TextStyle(
                              color: selectedCityName != null ? Colors.black : Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showCountryDialog() {
    if (isLoadingCountries) return;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Country'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: countries.length,
              itemBuilder: (context, index) {
                final country = countries[index];
                
                return ListTile(
                  title: Text(country.name),
                  onTap: () {
                    setState(() {
                      selectedCountry = country;
                      selectedState = null;
                      selectedCityName = null;
                      states = [];
                      cities = [];
                    });
                    widget.onCountryChanged(country.isoCode, country.name);
                    _loadStates();
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  void _showStateDialog() {
    if (isLoadingStates || states.isEmpty) {
      // If no states available, show a message
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('No States Available'),
            content: const Text('No states/provinces are available for the selected country. You can proceed to enter the city directly.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select State'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: states.length,
              itemBuilder: (context, index) {
                final state = states[index];
                
                return ListTile(
                  title: Text(state.name),
                  onTap: () {
                    setState(() {
                      selectedState = state;
                      selectedCityName = null;
                      cities = [];
                    });
                    widget.onStateChanged(state.isoCode, state.name);
                    _loadCities();
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  void _showCityDialog() {
    if (isLoadingCities) return;

    if (cities.isEmpty) {
      // Show message that no cities are available
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('No Cities Available'),
            content: const Text('No cities are available for the selected state.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Select City (${cities.length} available)'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: cities.length,
              itemBuilder: (context, index) {
                final city = cities[index];

                return ListTile(
                  title: Text(city.name),
                  onTap: () {
                    setState(() {
                      selectedCityName = city.name;
                    });
                    widget.onCityChanged(city.name);
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }
}