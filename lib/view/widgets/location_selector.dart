import 'package:flutter/material.dart';
import 'package:platix/utils/app_export.dart';

class LocationSelector extends StatefulWidget {
  final String? initialCountryId;
  final String? initialStateId;
  final String? initialCityName;
  final Function(String? countryId, String countryName) onCountryChanged;
  final Function(String? stateId, String stateName) onStateChanged;
  final Function(String cityName) onCityChanged;

  const LocationSelector({
    super.key,
    this.initialCountryId,
    this.initialStateId,
    this.initialCityName,
    required this.onCountryChanged,
    required this.onStateChanged,
    required this.onCityChanged,
  });

  @override
  State<LocationSelector> createState() => _LocationSelectorState();
}

class _LocationSelectorState extends State<LocationSelector> {
  // Predefined country data with IDs (commonly used countries)
  final Map<String, String> countries = {
    'IN': 'India',
    'US': 'United States',
    'GB': 'United Kingdom',
    'CA': 'Canada',
    'AU': 'Australia',
    'DE': 'Germany',
    'FR': 'France',
    'JP': 'Japan',
    'CN': 'China',
    'BR': 'Brazil',
  };

  // Predefined state data for India (most common use case)
  final Map<String, Map<String, String>> statesByCountry = {
    'IN': {
      'AN': 'Andaman and Nicobar Islands',
      'AP': 'Andhra Pradesh',
      'AR': 'Arunachal Pradesh',
      'AS': 'Assam',
      'BR': 'Bihar',
      'CG': 'Chhattisgarh',
      'CH': 'Chandigarh',
      'DH': 'Dadra and Nagar Haveli',
      'DD': 'Daman and Diu',
      'DL': 'Delhi',
      'GA': 'Goa',
      'GJ': 'Gujarat',
      'HR': 'Haryana',
      'HP': 'Himachal Pradesh',
      'JK': 'Jammu and Kashmir',
      'JH': 'Jharkhand',
      'KA': 'Karnataka',
      'KL': 'Kerala',
      'LA': 'Ladakh',
      'LD': 'Lakshadweep',
      'MP': 'Madhya Pradesh',
      'MH': 'Maharashtra',
      'MN': 'Manipur',
      'ML': 'Meghalaya',
      'MZ': 'Mizoram',
      'NL': 'Nagaland',
      'OR': 'Odisha',
      'PY': 'Puducherry',
      'PB': 'Punjab',
      'RJ': 'Rajasthan',
      'SK': 'Sikkim',
      'TN': 'Tamil Nadu',
      'TS': 'Telangana',
      'TR': 'Tripura',
      'UP': 'Uttar Pradesh',
      'UK': 'Uttarakhand',
      'WB': 'West Bengal',
    },
    'US': {
      'AL': 'Alabama',
      'AK': 'Alaska',
      'AZ': 'Arizona',
      'AR': 'Arkansas',
      'CA': 'California',
      'CO': 'Colorado',
      'CT': 'Connecticut',
      'DE': 'Delaware',
      'FL': 'Florida',
      'GA': 'Georgia',
    },
  };

  // Sample cities for major states (you can expand this)
  final Map<String, List<String>> citiesByState = {
    'MH': ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad'],
    'KA': ['Bangalore', 'Mysore', 'Hubli', 'Mangalore', 'Belgaum'],
    'TN': ['Chennai', 'Coimbatore', 'Madurai', 'Salem', 'Tiruchirappalli'],
    'DL': ['New Delhi', 'Central Delhi', 'South Delhi', 'North Delhi', 'East Delhi'],
    'GJ': ['Ahmedabad', 'Surat', 'Vadodara', 'Rajkot', 'Bhavnagar'],
    'CA': ['Los Angeles', 'San Francisco', 'San Diego', 'Sacramento', 'Oakland'],
  };

  String? selectedCountryId;
  String? selectedStateId;
  String? selectedCityName;

  @override
  void initState() {
    super.initState();
    _initializeSelections();
  }

  void _initializeSelections() {
    // Set initial values if provided
    selectedCountryId = widget.initialCountryId;
    selectedStateId = widget.initialStateId;
    selectedCityName = widget.initialCityName;
  }

  List<String> get availableStates {
    if (selectedCountryId == null) return [];
    return statesByCountry[selectedCountryId]?.values.toList() ?? [];
  }

  List<String> get availableCities {
    if (selectedStateId == null) return [];
    return citiesByState[selectedStateId] ?? [];
  }

  String? getStateIdByName(String stateName) {
    if (selectedCountryId == null) return null;
    final states = statesByCountry[selectedCountryId];
    if (states == null) return null;
    
    for (var entry in states.entries) {
      if (entry.value == stateName) {
        return entry.key;
      }
    }
    return null;
  }

  String? getStateNameById(String stateId) {
    if (selectedCountryId == null) return null;
    return statesByCountry[selectedCountryId]?[stateId];
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Country Dropdown
        Text(
          'Country',
          style: CustomTextStyles.b4_1,
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: selectedCountryId,
              hint: const Text('Select Country'),
              isExpanded: true,
              items: countries.entries.map((entry) {
                return DropdownMenuItem<String>(
                  value: entry.key,
                  child: Text(entry.value),
                );
              }).toList(),
              onChanged: (String? newCountryId) {
                setState(() {
                  selectedCountryId = newCountryId;
                  selectedStateId = null;
                  selectedCityName = null;
                });
                
                if (newCountryId != null) {
                  widget.onCountryChanged(newCountryId, countries[newCountryId]!);
                } else {
                  widget.onCountryChanged(null, '');
                }
              },
            ),
          ),
        ),
        
        const SizedBox(height: 15),
        
        // State Dropdown
        Text(
          'State',
          style: CustomTextStyles.b4_1,
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: selectedStateId != null ? getStateNameById(selectedStateId!) : null,
              hint: const Text('Select State'),
              isExpanded: true,
              items: availableStates.map((stateName) {
                return DropdownMenuItem<String>(
                  value: stateName,
                  child: Text(stateName),
                );
              }).toList(),
              onChanged: selectedCountryId == null ? null : (String? newStateName) {
                if (newStateName != null) {
                  final stateId = getStateIdByName(newStateName);
                  setState(() {
                    selectedStateId = stateId;
                    selectedCityName = null;
                  });
                  widget.onStateChanged(stateId, newStateName);
                } else {
                  setState(() {
                    selectedStateId = null;
                    selectedCityName = null;
                  });
                  widget.onStateChanged(null, '');
                }
              },
            ),
          ),
        ),
        
        const SizedBox(height: 15),
        
        // City Dropdown
        Text(
          'City',
          style: CustomTextStyles.b4_1,
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: selectedCityName,
              hint: const Text('Select City'),
              isExpanded: true,
              items: availableCities.map((cityName) {
                return DropdownMenuItem<String>(
                  value: cityName,
                  child: Text(cityName),
                );
              }).toList(),
              onChanged: selectedStateId == null ? null : (String? newCity) {
                setState(() {
                  selectedCityName = newCity;
                });
                
                if (newCity != null) {
                  widget.onCityChanged(newCity);
                }
              },
            ),
          ),
        ),
      ],
    );
  }
}