// ignore_for_file: prefer_const_constructors

import 'package:get/route_manager.dart';
import 'package:platix/view/screens/technician/tech_bottombar_screen.dart';
import 'package:platix/view/screens/mobile_otp_screen.dart';
import 'package:platix/view/screens/signinOption_screen.dart';
import 'package:platix/view/screens/signin_screen.dart';
import 'package:platix/view/screens/technician/tech_home_screen.dart';
import 'package:platix/view/screens/technician/tech_order_details_screen.dart';
import 'package:platix/view/screens/technician/tech_orderlist_screen.dart';
import 'package:platix/view/screens/technician/tech_profile_screen.dart';
import 'package:platix/view/screens/contact_us_screen.dart';
import 'package:platix/view/screens/refund_policy_screen.dart';
import 'package:platix/view/screens/store_homescreen.dart';
import 'package:platix/view/screens/dentist/item_details_screen.dart';
import 'package:platix/view/screens/cart_screen.dart';
import 'package:platix/bindings/cart_binding.dart';
import 'package:platix/bindings/item_details_binding.dart';
import 'package:platix/bindings/patient_registration_binding.dart';
import 'package:platix/view/screens/payment_success_screen.dart';
import 'package:platix/view/screens/dentist/patient_registration_screen.dart'; // New Patient Registration Screen
import 'package:platix/view/screens/dentist/create_appointment_screen.dart'; // Renamed Create Appointment Screen


import '../view/screens/onboarding_screens.dart';

class AppRoutes {
  static String initial = "/";
  static String signinOptionScreen = "/";
  static String signinScreen = "/";
  static String otpScreen = "/";
  static String techbottombar = "/";
  static String techHomeScreen = "/";
  static String techOrderlistScreen = "/";
  static String techProfileScreen = "/";
  static String contactUsScreen = "/contact-us";
  static String storeHomeScreen = "/store-home";
  static String itemDetailsScreen = "/item-details";
  static String cartScreen = "/cart";
  static String paymentSuccessScreen = "/payment-success";
  static String patientRegistrationScreen = "/patient-registration"; // New route
  static String createAppointmentScreen = "/create-appointment"; // New route for renamed screen
}

final getPages = [
  GetPage(
    name: AppRoutes.initial,
    // page: () => OnboardingScreens(),
     //page: () => TechHomeScreen(),
     page: () => TechBottombarScreen(),
  ),
  GetPage(
      name: AppRoutes.signinOptionScreen,
      page: () =>SigninOptionScreen()
  ),
  GetPage(
      name: AppRoutes.signinScreen,
      page: () => SigninScreen()
  ),
  // GetPage(
  //     name: AppRoutes.otpScreen,
  //     page: () => MobileOtpScreen()
  // ),
  GetPage(
      name: AppRoutes.techbottombar,
      page: () => TechBottombarScreen()
  ),
  GetPage(
      name: AppRoutes.techHomeScreen,
      page: () => TechHomeScreen()
  ),
  GetPage(
      name: AppRoutes.techOrderlistScreen,
      page: () => TechOrderlistScreen()
  ),
  GetPage(
      name: AppRoutes.techProfileScreen,
      page: () => TechProfileScreen()
  ),
  GetPage(
    name: AppRoutes.contactUsScreen,
    page: () => const ContactUsScreen(),
  ),
  GetPage(
    name: '/refund-policy',
    page: () => const RefundPolicyScreen(),
  ),
  GetPage(
    name: AppRoutes.storeHomeScreen,
    page: () => const StoreHomeScreen(),
    binding: CartBinding(),
  ),
  GetPage(
    name: AppRoutes.cartScreen,
    page: () => const CartScreen(),
    binding: CartBinding(),
  ),
  GetPage(
    name: AppRoutes.itemDetailsScreen,
    page: () => ItemDetailsScreen(item: Get.arguments),
    binding: ItemDetailsBinding(),
  ),
  GetPage(
    name: AppRoutes.paymentSuccessScreen,
    page: () => PaymentSuccessScreen(orderId: Get.arguments as String),
  ),
  GetPage(
    name: AppRoutes.patientRegistrationScreen,
    page: () => const PatientRegistrationScreen(),
    binding: PatientRegistrationBinding(),
  ),
  GetPage(
    name: AppRoutes.createAppointmentScreen,
    page: () => const CreateAppointmentScreen(),
  ),
];
