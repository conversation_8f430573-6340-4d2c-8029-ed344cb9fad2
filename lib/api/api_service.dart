import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:platix/api/config.dart';
import 'package:platix/data/models/payment_response_model.dart';
import 'package:platix/data/models/role_permission_model.dart';
import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/data/models/patient_upsert_response_model.dart';
import 'package:platix/data/models/patient_delete_response_model.dart';

class ApiService {
  final Dio dio;
  final GetStorage _box = GetStorage();

  ApiService() : dio = Dio(BaseOptions(baseUrl: Config.baseUrl)) {
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final token = _box.read('token'); // Assuming 'token' is the key for your auth token
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        return handler.next(options);
      },
      onError: (DioException e, handler) {
        // You can add error handling logic here, e.g., for token expiration
        return handler.next(e);
      },
    ));
  }

  Future<RolePermissionModel> getRolePermissions(bool mode) async {
    print("getRolePermissions");
    try {
      final response = await dio.post(
        Config.getRolePermissions,
        data: {"mode": mode},
      );
      print("getRolePermissions response: ${response.data}");
      if (response.data['status'] == true) {
        _box.write('user_permissions', response.data['data']);
      }
      return RolePermissionModel.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception(e.message);
    }
  }

  Future<PatientRegistrationModel> getAllRegistrations({
    int page = 1,
    int limit = 10,
    String search = "",
  }) async {
    print("getAllRegistrations - Page: $page, Limit: $limit, Search: $search");
    try {
      final response = await dio.get(
        Config.getAllRegistrations,
        queryParameters: {
          'page': page,
          'limit': limit,
          'search': search,
        },
      );
      print("getAllRegistrations response: ${response.data}");
      return PatientRegistrationModel.fromJson(response.data);
    } on DioException catch (e) {
      print("getAllRegistrations error: ${e.message}");
      throw Exception(e.message ?? 'Failed to fetch patient registrations');
    }
  }

  Future<PatientUpsertResponse> upsertPatient({
    int? id, // Optional for update
    String? userId,
    required String firstName,
    required String lastName,
    required String mobile,
    required String email,
    required String age,
    required String gender,
    String? country,
    String? state,
    String? city,
    String? address,
    String? dob,
    bool force = false, // For handling duplicate mobile numbers
  }) async {
    print("upsertPatient - ID: $id, Mobile: $mobile, Force: $force");
    try {
      final Map<String, dynamic> requestData = {
        'firstName': firstName,
        'lastName': lastName,
        'mobile': mobile,
        'email': email,
        'age': age,
        'gender': gender,
        'force': force,
      };

      // Add optional fields if provided
      if (id != null) requestData['id'] = id;
      if (userId != null) requestData['user_id'] = userId;
      if (country != null) requestData['country'] = country;
      if (state != null) requestData['state'] = state;
      if (city != null) requestData['city'] = city;
      if (address != null) requestData['address'] = address;
      if (dob != null) requestData['dob'] = dob;

      final response = await dio.post(
        Config.upsertPatientRegistration,
        data: requestData,
      );
      
      print("upsertPatient response: ${response.data}");
      print("upsertPatient status code: ${response.statusCode}");
      print("upsertPatient response type: ${response.data.runtimeType}");
      
      // Handle both 200 and 201 status codes - but 201 can have status:false for duplicates
      if (response.statusCode == 200 || response.statusCode == 201) {
        try {
          final parsedResponse = PatientUpsertResponse.fromJson(response.data);
          print("Parsed response - Status: ${parsedResponse.status}, Message: ${parsedResponse.message}");
          
          // Both success and duplicate cases return here
          // The Flutter screen will check parsedResponse.status to determine the flow
          return parsedResponse;
        } catch (parseError) {
          print("Error parsing response: $parseError");
          print("Raw response data: ${response.data}");
          throw Exception('Failed to parse response: $parseError');
        }
      } else {
        throw Exception('Unexpected status code: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("upsertPatient error: ${e.message}");
      print("upsertPatient error status code: ${e.response?.statusCode}");
      
      // Handle actual server errors (500, 404, etc.)
      if (e.response?.data != null) {
        try {
          return PatientUpsertResponse.fromJson(e.response!.data);
        } catch (parseError) {
          print("Error parsing error response: $parseError");
        }
      }
      
      throw Exception(e.message ?? 'Failed to save patient registration');
    }
  }

  Future<PatientDeleteResponse> deletePatient(String patientId) async {
    print("deletePatient - ID: $patientId");
    try {
      final response = await dio.delete(
        Config.deletePatientRegistration(patientId),
      );
      
      print("deletePatient response: ${response.data}");
      print("deletePatient status code: ${response.statusCode}");
      
      // Handle 200 status code as success
      if (response.statusCode == 200) {
        return PatientDeleteResponse.fromJson(response.data);
      } else {
        throw Exception('Unexpected status code: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("deletePatient error: ${e.message}");
      print("deletePatient error status code: ${e.response?.statusCode}");
      
      // Handle specific error cases
      if (e.response?.data != null) {
        return PatientDeleteResponse.fromJson(e.response!.data);
      }
      
      throw Exception(e.message ?? 'Failed to delete patient registration');
    }
  }
}
